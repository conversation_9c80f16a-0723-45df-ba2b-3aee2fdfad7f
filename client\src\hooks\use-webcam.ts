import { useState, useCallback, useRef } from 'react';

export function useWebcam() {
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isActive, setIsActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  const startWebcam = useCallback(async () => {
    try {
      setError(null);
      
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user'
        },
        audio: false
      });
      
      setStream(mediaStream);
      setIsActive(true);
      
      // If video element exists, set the stream
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to access webcam';
      setError(errorMessage);
      console.warn('Webcam access denied or not available:', err);
    }
  }, []);

  const stopWebcam = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
      setIsActive(false);
      
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
    }
  }, [stream]);

  const captureImage = useCallback((): string | null => {
    if (!stream || !videoRef.current) return null;
    
    try {
      const canvas = document.createElement('canvas');
      const video = videoRef.current;
      
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) return null;
      
      ctx.drawImage(video, 0, 0);
      return canvas.toDataURL('image/jpeg', 0.8);
    } catch (err) {
      console.error('Capture error:', err);
      return null;
    }
  }, [stream]);

  const switchCamera = useCallback(async () => {
    if (stream) {
      stopWebcam();
      // Add a small delay before starting with different constraints
      setTimeout(() => {
        startWebcam();
      }, 100);
    }
  }, [stream, stopWebcam, startWebcam]);

  return {
    stream,
    isActive,
    error,
    videoRef,
    startWebcam,
    stopWebcam,
    captureImage,
    switchCamera
  };
}
