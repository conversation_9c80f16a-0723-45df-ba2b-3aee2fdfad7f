import { useState, useRef } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { insertPessoaSchema, type Pessoa, type InsertPessoa } from "@shared/schema";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { Camera, Upload, Trash2, Edit } from "lucide-react";

export default function CadastroPessoas() {
  const [fotoPreview, setFotoPreview] = useState<string | null>(null);
  const [editandoPessoa, setEditandoPessoa] = useState<Pessoa | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const form = useForm<InsertPessoa>({
    resolver: zodResolver(insertPessoaSchema),
    defaultValues: {
      nome: "",
      foto: "",
      descricao: "",
    },
  });

  const { data: pessoas = [], isLoading } = useQuery<Pessoa[]>({
    queryKey: ["/api/pessoas"],
  });

  const criarPessoaMutation = useMutation({
    mutationFn: async (data: InsertPessoa) => {
      return await apiRequest("/api/pessoas", "POST", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/pessoas"] });
      form.reset();
      setFotoPreview(null);
      toast({
        title: "Sucesso!",
        description: "Pessoa cadastrada com sucesso.",
      });
    },
    onError: () => {
      toast({
        title: "Erro",
        description: "Erro ao cadastrar pessoa.",
        variant: "destructive",
      });
    },
  });

  const atualizarPessoaMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<InsertPessoa> }) => {
      return await apiRequest(`/api/pessoas/${id}`, "PUT", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/pessoas"] });
      setEditandoPessoa(null);
      form.reset();
      setFotoPreview(null);
      toast({
        title: "Sucesso!",
        description: "Pessoa atualizada com sucesso.",
      });
    },
    onError: () => {
      toast({
        title: "Erro",
        description: "Erro ao atualizar pessoa.",
        variant: "destructive",
      });
    },
  });

  const deletarPessoaMutation = useMutation({
    mutationFn: async (id: number) => {
      return await apiRequest(`/api/pessoas/${id}`, "DELETE");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/pessoas"] });
      toast({
        title: "Sucesso!",
        description: "Pessoa removida com sucesso.",
      });
    },
    onError: () => {
      toast({
        title: "Erro",
        description: "Erro ao remover pessoa.",
        variant: "destructive",
      });
    },
  });

  const handleFotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64 = e.target?.result as string;
        setFotoPreview(base64);
        form.setValue("foto", base64);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = (data: InsertPessoa) => {
    if (editandoPessoa) {
      atualizarPessoaMutation.mutate({ id: editandoPessoa.id, data });
    } else {
      criarPessoaMutation.mutate(data);
    }
  };

  const iniciarEdicao = (pessoa: Pessoa) => {
    setEditandoPessoa(pessoa);
    form.setValue("nome", pessoa.nome);
    form.setValue("foto", pessoa.foto);
    form.setValue("descricao", pessoa.descricao || "");
    setFotoPreview(pessoa.foto);
  };

  const cancelarEdicao = () => {
    setEditandoPessoa(null);
    form.reset();
    setFotoPreview(null);
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Cadastro de Pessoas</h1>
        <p className="text-muted-foreground">
          Gerencie o cadastro de pessoas com nome e foto para reconhecimento facial
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Formulário de Cadastro */}
        <Card>
          <CardHeader>
            <CardTitle>
              {editandoPessoa ? "Editar Pessoa" : "Nova Pessoa"}
            </CardTitle>
            <CardDescription>
              {editandoPessoa 
                ? "Edite as informações da pessoa selecionada"
                : "Cadastre uma nova pessoa com nome e foto"
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="nome"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nome</FormLabel>
                      <FormControl>
                        <Input placeholder="Digite o nome completo" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="foto"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Foto</FormLabel>
                      <FormControl>
                        <div className="space-y-2">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleFotoUpload}
                            ref={fileInputRef}
                            className="hidden"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => fileInputRef.current?.click()}
                            className="w-full"
                          >
                            <Upload className="mr-2 h-4 w-4" />
                            Selecionar Foto
                          </Button>
                          {fotoPreview && (
                            <div className="mt-2">
                              <img
                                src={fotoPreview}
                                alt="Preview"
                                className="w-32 h-32 object-cover rounded-lg border"
                              />
                            </div>
                          )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="descricao"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Descrição (opcional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Adicione uma descrição ou observação sobre a pessoa"
                          rows={3}
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex gap-2">
                  <Button 
                    type="submit" 
                    disabled={criarPessoaMutation.isPending || atualizarPessoaMutation.isPending}
                    className="flex-1"
                  >
                    {editandoPessoa ? "Atualizar" : "Cadastrar"}
                  </Button>
                  {editandoPessoa && (
                    <Button 
                      type="button" 
                      variant="outline"
                      onClick={cancelarEdicao}
                    >
                      Cancelar
                    </Button>
                  )}
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Lista de Pessoas */}
        <Card>
          <CardHeader>
            <CardTitle>Pessoas Cadastradas</CardTitle>
            <CardDescription>
              Lista de todas as pessoas no sistema
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-4">Carregando...</div>
            ) : pessoas.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Nenhuma pessoa cadastrada ainda
              </div>
            ) : (
              <div className="space-y-4">
                {pessoas.map((pessoa: Pessoa) => (
                  <div key={pessoa.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <img
                      src={pessoa.foto}
                      alt={pessoa.nome}
                      className="w-16 h-16 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <h3 className="font-semibold">{pessoa.nome}</h3>
                      {pessoa.descricao && (
                        <p className="text-sm text-muted-foreground">{pessoa.descricao}</p>
                      )}
                      <p className="text-xs text-muted-foreground">
                        Cadastrado em: {new Date(pessoa.criadoEm || "").toLocaleDateString("pt-BR")}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => iniciarEdicao(pessoa)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => deletarPessoaMutation.mutate(pessoa.id)}
                        disabled={deletarPessoaMutation.isPending}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}