import { useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useHumanLibrary } from "@/hooks/use-human-library";

interface ImageUploadProps {
  config: any;
  onResults: (results: any) => void;
  onPerformance: (performance: any) => void;
}

export default function ImageUpload({ config, onResults, onPerformance }: ImageUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const { detect, isInitialized, isLoading, error } = useHumanLibrary(config);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setSelectedImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleProcessImage = async () => {
    if (!selectedImage || !isInitialized || !imageRef.current) return;
    
    setIsProcessing(true);
    try {
      const startTime = performance.now();
      const results = await detect(imageRef.current);
      const endTime = performance.now();
      
      const performanceData = {
        ...results.performance,
        total: endTime - startTime
      };
      
      onResults(results);
      onPerformance(performanceData);
    } catch (error) {
      console.error('Processing error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0 && files[0].type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setSelectedImage(e.target?.result as string);
      };
      reader.readAsDataURL(files[0]);
    }
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium text-gray-900">Image Upload</CardTitle>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Static Analysis</span>
            <button className="text-gray-400 hover:text-gray-600">
              📁
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div 
          className="relative bg-gray-100 rounded-lg aspect-video flex items-center justify-center overflow-hidden border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors"
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {selectedImage ? (
            <>
              <img
                ref={imageRef}
                src={selectedImage}
                alt="Uploaded image"
                className="w-full h-full object-contain"
                onLoad={() => {
                  // Auto-process when image loads if library is ready
                  if (isInitialized && !isProcessing) {
                    handleProcessImage();
                  }
                }}
              />
              {/* Human Library Status Overlay */}
              {!isInitialized && (
                <div className="absolute top-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                  {isLoading ? 'Loading Human Library...' : error ? 'Library Error' : 'Initializing...'}
                </div>
              )}
              {isInitialized && (
                <div className="absolute top-2 left-2 bg-green-600 bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                  Human Library Ready
                </div>
              )}
              {isProcessing && (
                <div className="absolute top-2 right-2 bg-blue-600 bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                  Processing...
                </div>
              )}
            </>
          ) : (
            <div className="text-center">
              <div className="text-4xl mb-2">🖼️</div>
              <p className="text-gray-500 mb-2">Drop an image here or click to select</p>
              <Button 
                onClick={() => fileInputRef.current?.click()} 
                className="bg-blue-600 hover:bg-blue-700"
              >
                Select Image
              </Button>
            </div>
          )}
        </div>
        
        {/* Upload Controls */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
            >
              📁 Browse
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleProcessImage}
              disabled={!selectedImage || !isInitialized || isProcessing}
            >
              {isProcessing ? "⏳ Processing..." : "🔍 Analyze"}
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            {selectedImage && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedImage(null)}
              >
                🗑️ Clear
              </Button>
            )}
          </div>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        {/* Sample Images */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Sample Images</h4>
          <div className="grid grid-cols-3 gap-2">
            <button className="bg-gray-100 rounded-lg p-2 text-xs text-gray-600 hover:bg-gray-200">
              👤 Portrait
            </button>
            <button className="bg-gray-100 rounded-lg p-2 text-xs text-gray-600 hover:bg-gray-200">
              👥 Group
            </button>
            <button className="bg-gray-100 rounded-lg p-2 text-xs text-gray-600 hover:bg-gray-200">
              😊 Emotion
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}