# Human Library Testing Tool

## Overview

This is a comprehensive testing tool for Vladmandic's Human library, a 3D facial recognition and body pose estimation system. The application provides a real-time webcam interface for testing and tuning various parameters of the Human library with live performance monitoring and result visualization.

## System Architecture

### Frontend Architecture
- **Framework**: React with TypeScript
- **Build Tool**: Vite for development and production builds
- **UI Framework**: Tailwind CSS with shadcn/ui components
- **State Management**: React hooks and React Query for server state
- **Routing**: Wouter for client-side routing

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Type System**: TypeScript throughout
- **Database**: PostgreSQL with Drizzle ORM
- **Session Management**: PostgreSQL-based session storage
- **API**: RESTful endpoints for configuration and test result management

### Database Schema
- **Users**: Basic user authentication (id, username, password)
- **Test Configurations**: Stored parameter configurations with preset support
- **Test Results**: Performance metrics, detection results, and processing statistics

## Key Components

### Core Features
1. **Real-time Webcam Processing**: Live video capture and processing with the Human library
2. **Parameter Configuration**: Comprehensive controls for all Human library parameters
3. **Performance Monitoring**: Real-time FPS, processing time, and resource usage tracking
4. **Result Visualization**: Live detection results with confidence scores and face metrics
5. **Preset Management**: Save and load configuration presets for different use cases

### Frontend Components
- **WebcamCapture**: Handles video stream and image processing
- **ParameterControls**: Dynamic form controls for Human library configuration
- **DetectionResults**: Real-time visualization of detection output
- **PerformanceMonitor**: System metrics and performance tracking

### Backend Services
- **Storage Layer**: Abstracted storage interface with in-memory implementation
- **API Routes**: RESTful endpoints for configurations and test results
- **Session Management**: User authentication and session handling

## Data Flow

1. User configures Human library parameters through the UI
2. Webcam captures video frames at regular intervals
3. Frames are processed through the Human library with current configuration
4. Results are displayed in real-time including:
   - Face detection coordinates and confidence scores
   - Performance metrics (processing time, FPS)
   - System resource usage
5. Test results can be saved to the database for analysis
6. Users can load preset configurations for different scenarios

## External Dependencies

### Core Libraries
- **@vladmandic/human**: 3D face recognition and body pose estimation
- **@neondatabase/serverless**: PostgreSQL database connectivity
- **@tanstack/react-query**: Server state management
- **@radix-ui/react-***: Accessible UI component primitives

### Development Tools
- **Drizzle ORM**: Type-safe database operations
- **Vite**: Fast development and build tooling
- **TypeScript**: Static type checking
- **Tailwind CSS**: Utility-first CSS framework

## Deployment Strategy

### Development
- Vite dev server for frontend hot reloading
- Express server with TypeScript compilation via tsx
- Development-specific middleware for error handling

### Production
- Vite build process creates optimized static assets
- esbuild bundles the Express server for Node.js runtime
- Static files served from the Express server
- Environment-based configuration for database connections

### Database
- PostgreSQL database with Drizzle migrations
- Connection pooling for production environments
- Environment variable configuration for database URLs

## Changelog
- July 03, 2025. Initial setup
- July 03, 2025. Successfully integrated @vladmandic/human library v3.3.5 for real 3D facial recognition
- July 03, 2025. Fixed model path configuration issues and optimized for basic face detection
- July 03, 2025. Application now performs real-time facial recognition with webcam capture

## Recent Progress
✓ Real Human library integration (v3.3.5) instead of mock data
✓ Webcam capture with live facial recognition processing  
✓ Parameter controls for all Human library settings
✓ Real-time detection results with face analysis
✓ Performance monitoring with actual processing metrics
✓ Simplified configuration to ensure stable face detection
✓ Complete translation to Brazilian Portuguese (PT-BR)
✓ PostgreSQL database implementation for people management
✓ People registration system with name and photo storage
✓ Navigation between facial recognition and people management pages
✓ Database-backed storage replacing in-memory storage

## User Preferences

Preferred language: Brazilian Portuguese (PT-BR)
Preferred communication style: Simple, everyday language.