import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { TestConfiguration } from "@shared/schema";

interface ParameterControlsProps {
  config: any;
  onConfigChange: (key: string, value: any) => void;
  configurations: TestConfiguration[];
  onLoadPreset: (config: any) => void;
}

export default function ParameterControls({ config, onConfigChange, configurations, onLoadPreset }: ParameterControlsProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    backend: true,
    face: true,
    body: false,
    hand: false,
    performance: false
  });

  const presets = configurations.filter(c => c.isPreset);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handlePresetSelect = (preset: TestConfiguration) => {
    onLoadPreset(preset.config);
  };

  return (
    <div className="p-4">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Configuration Parameters</h2>
      
      {/* Search Parameters */}
      <div className="mb-4">
        <div className="relative">
          <span className="absolute left-3 top-3 text-gray-400">🔍</span>
          <Input
            type="text"
            placeholder="Search parameters..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Preset Configurations */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-2">Quick Presets</h3>
        <div className="grid grid-cols-2 gap-2">
          {presets.map((preset) => (
            <Button
              key={preset.id}
              variant="outline"
              size="sm"
              onClick={() => handlePresetSelect(preset)}
              className="text-xs"
            >
              {preset.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Backend Configuration */}
      <Collapsible open={expandedSections.backend} onOpenChange={() => toggleSection('backend')}>
        <CollapsibleTrigger className="flex items-center justify-between w-full mb-3">
          <h3 className="text-sm font-medium text-gray-900">Backend Configuration</h3>
          <span className="text-gray-400">{expandedSections.backend ? '⬇️' : '➡️'}</span>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 mb-6">
          <div>
            <Label className="text-xs font-medium text-gray-700 mb-1">Backend Engine</Label>
            <Select value={config.backend} onValueChange={(value) => onConfigChange('backend', value)}>
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="webgl">WebGL (Recommended)</SelectItem>
                <SelectItem value="wasm">WASM</SelectItem>
                <SelectItem value="cpu">CPU</SelectItem>
                <SelectItem value="humangl">HumanGL</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label className="text-xs font-medium text-gray-700 mb-1">Model Path</Label>
            <Input
              type="text"
              value={config.modelPath}
              onChange={(e) => onConfigChange('modelPath', e.target.value)}
              className="font-mono text-xs"
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-700">Debug Mode</Label>
            <Switch
              checked={config.debug}
              onCheckedChange={(checked) => onConfigChange('debug', checked)}
            />
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Face Detection */}
      <Collapsible open={expandedSections.face} onOpenChange={() => toggleSection('face')}>
        <CollapsibleTrigger className="flex items-center justify-between w-full mb-3">
          <h3 className="text-sm font-medium text-gray-900">Face Detection</h3>
          <span className="text-gray-400">{expandedSections.face ? '⬇️' : '➡️'}</span>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 mb-6">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-700">Enabled</Label>
            <Switch
              checked={config.face?.enabled}
              onCheckedChange={(checked) => onConfigChange('face.enabled', checked)}
            />
          </div>
          <div>
            <Label className="text-xs font-medium text-gray-700 mb-1">Detector Model</Label>
            <Select 
              value={config.face?.detector} 
              onValueChange={(value) => onConfigChange('face.detector', value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mediapipe">MediaPipe</SelectItem>
                <SelectItem value="mtcnn">MTCNN</SelectItem>
                <SelectItem value="retinaface">RetinaFace</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-700">3D Face Mesh</Label>
            <Switch
              checked={config.face?.mesh}
              onCheckedChange={(checked) => onConfigChange('face.mesh', checked)}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-700">Iris Detection</Label>
            <Switch
              checked={config.face?.iris}
              onCheckedChange={(checked) => onConfigChange('face.iris', checked)}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-700">Emotion Detection</Label>
            <Switch
              checked={config.face?.emotion}
              onCheckedChange={(checked) => onConfigChange('face.emotion', checked)}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-700">Age Prediction</Label>
            <Switch
              checked={config.face?.age}
              onCheckedChange={(checked) => onConfigChange('face.age', checked)}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-700">Gender Prediction</Label>
            <Switch
              checked={config.face?.gender}
              onCheckedChange={(checked) => onConfigChange('face.gender', checked)}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-700">Anti-spoofing</Label>
            <Switch
              checked={config.face?.antispoof}
              onCheckedChange={(checked) => onConfigChange('face.antispoof', checked)}
            />
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Body Pose */}
      <Collapsible open={expandedSections.body} onOpenChange={() => toggleSection('body')}>
        <CollapsibleTrigger className="flex items-center justify-between w-full mb-3">
          <h3 className="text-sm font-medium text-gray-900">Body Pose</h3>
          <span className="text-gray-400">{expandedSections.body ? '⬇️' : '➡️'}</span>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 mb-6">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-700">Enabled</Label>
            <Switch
              checked={config.body?.enabled}
              onCheckedChange={(checked) => onConfigChange('body.enabled', checked)}
            />
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Hand Tracking */}
      <Collapsible open={expandedSections.hand} onOpenChange={() => toggleSection('hand')}>
        <CollapsibleTrigger className="flex items-center justify-between w-full mb-3">
          <h3 className="text-sm font-medium text-gray-900">Hand Tracking</h3>
          <span className="text-gray-400">{expandedSections.hand ? '⬇️' : '➡️'}</span>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 mb-6">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-700">Enabled</Label>
            <Switch
              checked={config.hand?.enabled}
              onCheckedChange={(checked) => onConfigChange('hand.enabled', checked)}
            />
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Performance Filters */}
      <Collapsible open={expandedSections.performance} onOpenChange={() => toggleSection('performance')}>
        <CollapsibleTrigger className="flex items-center justify-between w-full mb-3">
          <h3 className="text-sm font-medium text-gray-900">Performance Filters</h3>
          <span className="text-gray-400">{expandedSections.performance ? '⬇️' : '➡️'}</span>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 mb-6">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-700">Temporal Filtering</Label>
            <Switch
              checked={config.filter?.enabled}
              onCheckedChange={(checked) => onConfigChange('filter.enabled', checked)}
            />
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
