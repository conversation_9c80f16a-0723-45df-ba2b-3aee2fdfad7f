import { useState } from "react";
import ParameterControls from "@/components/parameter-controls";
import WebcamCapture from "@/components/webcam-capture";
import DetectionResults from "@/components/detection-results";
import PerformanceMonitor from "@/components/performance-monitor";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useQuery } from "@tanstack/react-query";
import { TestConfiguration } from "@shared/schema";

export default function TesteHuman() {
  const [currentConfig, setCurrentConfig] = useState<any>({
    backend: "cpu",
    modelBasePath: "https://cdn.jsdelivr.net/npm/@vladmandic/human-models/models/",
    debug: false,
    async: true,
    warmup: "none",
    cacheSensitivity: 0,
    deallocate: true,
    face: {
      enabled: true,
      detector: {
        modelPath: "blazeface.json",
        maxDetected: 5,
        minConfidence: 0.5,
        iouThreshold: 0.3,
        return: true
      },
      mesh: { enabled: false },
      iris: { enabled: false },
      description: { enabled: false },
      emotion: { enabled: false },
      age: { enabled: false },
      gender: { enabled: false }
    },
    body: { enabled: false },
    hand: { enabled: false },
    gesture: { enabled: false }
  });

  const [isProcessing, setIsProcessing] = useState(false);
  const [detectionResults, setDetectionResults] = useState<any>(null);
  const [performanceData, setPerformanceData] = useState<any>(null);

  const { data: configurations } = useQuery<TestConfiguration[]>({
    queryKey: ['/api/test-configurations'],
  });

  const handleConfigChange = (key: string, value: any) => {
    setCurrentConfig((prev: any) => {
      const keys = key.split('.');
      const newConfig = { ...prev };
      let current = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) current[keys[i]] = {};
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  const handleExportConfig = () => {
    const dataStr = JSON.stringify(currentConfig, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    const exportFileDefaultName = 'human-config.json';
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const handleImportConfig = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const config = JSON.parse(e.target?.result as string);
          setCurrentConfig(config);
        } catch (error) {
          console.error('Failed to parse configuration file:', error);
        }
      };
      reader.readAsText(file);
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="text-2xl mr-3">👨‍🚀</div>
              <h1 className="text-xl font-semibold text-gray-900">Teste da Biblioteca Human</h1>
              <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded-full font-medium">v3.3.5</span>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm" onClick={handleExportConfig}>
                📥 Exportar Configuração
              </Button>
              <div>
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImportConfig}
                  className="hidden"
                  id="import-config"
                />
                <Button variant="default" size="sm" onClick={() => document.getElementById('import-config')?.click()}>
                  📤 Importar Configuração
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex h-full overflow-hidden">
        {/* Left Sidebar - Parameter Controls */}
        <div className="w-80 bg-white shadow-lg border-r border-gray-200 overflow-y-auto">
          <ParameterControls
            config={currentConfig}
            onConfigChange={handleConfigChange}
            configurations={configurations || []}
            onLoadPreset={setCurrentConfig}
          />
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Input Controls */}
          <div className="bg-white border-b border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Tabs defaultValue="webcam" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="webcam">📹 Webcam</TabsTrigger>
                    <TabsTrigger value="upload" disabled>🖼️ Upload de Imagem</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${isProcessing ? 'bg-green-500 animate-pulse' : 'bg-gray-300'}`}></div>
                  <span className="text-sm text-gray-600">{isProcessing ? 'Processando' : 'Pronto'}</span>
                </div>
                
                <Button
                  variant={isProcessing ? "secondary" : "default"}
                  size="sm"
                  onClick={() => setIsProcessing(!isProcessing)}
                  className={isProcessing ? "bg-green-600 hover:bg-green-700" : ""}
                >
                  {isProcessing ? "⏸️ Pause" : "▶️ Start"}
                </Button>
              </div>
            </div>
          </div>

          {/* Main Processing Area */}
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
              <WebcamCapture
                config={currentConfig}
                isProcessing={isProcessing}
                onResults={setDetectionResults}
                onPerformance={setPerformanceData}
              />
              <DetectionResults
                results={detectionResults}
                isProcessing={isProcessing}
              />
            </div>
          </div>
        </div>

        {/* Right Sidebar - Performance Monitor */}
        <div className="w-80 bg-white shadow-lg border-l border-gray-200 overflow-y-auto">
          <PerformanceMonitor
            config={currentConfig}
            performanceData={performanceData}
            isProcessing={isProcessing}
          />
        </div>
      </div>

      {/* Bottom Status Bar */}
      <div className="bg-white border-t border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6 text-xs text-gray-500">
            <span>Library: @vladmandic/human v3.3.5</span>
            <span>Backend: {currentConfig.backend}</span>
            <span>Models: {isProcessing ? 'Processing...' : 'Ready'}</span>
          </div>
          <div className="flex items-center space-x-4 text-xs">
            <span className="text-gray-500">Session: Active</span>
            <span className="text-gray-500">Tests: 0</span>
            <button className="text-blue-600 hover:text-blue-700">
              ❓ Help
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
