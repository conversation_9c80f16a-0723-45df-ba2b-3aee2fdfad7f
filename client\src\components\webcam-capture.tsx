import { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useWebcam } from "@/hooks/use-webcam";
import { useHumanLibrary } from "@/hooks/use-human-library";

interface WebcamCaptureProps {
  config: any;
  isProcessing: boolean;
  onResults: (results: any) => void;
  onPerformance: (performance: any) => void;
}

export default function WebcamCapture({ config, isProcessing, onResults, onPerformance }: WebcamCaptureProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [resolution, setResolution] = useState("640x480");
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  
  const { stream, isActive, startWebcam, stopWebcam, captureImage } = useWebcam();
  const { detect, isInitialized, isLoading, error } = useHumanLibrary(config);

  useEffect(() => {
    if (stream && videoRef.current) {
      videoRef.current.srcObject = stream;
    }
  }, [stream]);

  useEffect(() => {
    if (isProcessing && isActive && videoRef.current) {
      const interval = setInterval(async () => {
        try {
          const startTime = performance.now();
          const results = await detect(videoRef.current!);
          const endTime = performance.now();
          
          const performanceData = {
            ...results.performance,
            total: endTime - startTime
          };
          
          onResults(results);
          onPerformance(performanceData);
        } catch (error) {
          console.warn('Processing error, skipping frame:', error);
          // Don't stop processing on individual frame errors
        }
      }, 500); // Slower processing to reduce load - every 500ms for ~2 FPS

      return () => clearInterval(interval);
    }
  }, [isProcessing, isActive, detect, onResults, onPerformance]);

  const handleCapture = () => {
    const imageData = captureImage();
    if (imageData) {
      setCapturedImage(imageData);
    }
  };

  const handleResolutionChange = (value: string) => {
    setResolution(value);
    // Here you would typically restart the webcam with new resolution
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium text-gray-900">Input Source</CardTitle>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">{resolution}</span>
            <button className="text-gray-400 hover:text-gray-600">
              🔍
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="relative bg-gray-100 rounded-lg aspect-video flex items-center justify-center overflow-hidden">
          {isActive ? (
            <>
              <video
                ref={videoRef}
                autoPlay
                muted
                playsInline
                className="w-full h-full object-cover"
              />
              <canvas
                ref={canvasRef}
                className="hidden"
              />
              {/* Human Library Status Overlay */}
              {!isInitialized && (
                <div className="absolute top-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                  {isLoading ? 'Loading Human Library...' : error ? 'Library Error' : 'Initializing...'}
                </div>
              )}
              {isInitialized && (
                <div className="absolute top-2 left-2 bg-green-600 bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                  Human Library Ready
                </div>
              )}
            </>
          ) : (
            <div className="text-center">
              <div className="text-4xl mb-2">📹</div>
              <p className="text-gray-500 mb-2">
                {error ? `Camera Error: ${error}` : 'Webcam feed will appear here'}
              </p>
              <Button 
                onClick={startWebcam} 
                className="bg-blue-600 hover:bg-blue-700"
                disabled={!!error && error.includes('denied')}
              >
                {error ? 'Retry Camera' : 'Enable Camera'}
              </Button>
              {error && (
                <p className="text-xs text-red-600 mt-2">
                  Please allow camera access in your browser settings
                </p>
              )}
            </div>
          )}
        </div>
        
        {/* Camera Controls */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCapture}
              disabled={!isActive}
            >
              📷 Capture
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={isActive ? stopWebcam : startWebcam}
            >
              {isActive ? "⏹️ Stop" : "📹 Start"}
            </Button>
          </div>
          <Select value={resolution} onValueChange={handleResolutionChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1280x720">1280x720</SelectItem>
              <SelectItem value="640x480">640x480</SelectItem>
              <SelectItem value="320x240">320x240</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
}
