import { users, testConfigurations, testResults, pessoas, type User, type InsertUser, type TestConfiguration, type InsertTestConfiguration, type TestResult, type InsertTestResult, type Pessoa, type InsertPessoa } from "@shared/schema";
import { db } from "./db";
import { eq } from "drizzle-orm";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  getTestConfiguration(id: number): Promise<TestConfiguration | undefined>;
  getTestConfigurations(): Promise<TestConfiguration[]>;
  createTestConfiguration(config: InsertTestConfiguration): Promise<TestConfiguration>;
  updateTestConfiguration(id: number, config: Partial<InsertTestConfiguration>): Promise<TestConfiguration | undefined>;
  deleteTestConfiguration(id: number): Promise<boolean>;
  
  getTestResults(configId?: number): Promise<TestResult[]>;
  createTestResult(result: InsertTestResult): Promise<TestResult>;
  getTestResultsByDateRange(startDate: Date, endDate: Date): Promise<TestResult[]>;
  
  getPessoa(id: number): Promise<Pessoa | undefined>;
  getPessoas(): Promise<Pessoa[]>;
  createPessoa(pessoa: InsertPessoa): Promise<Pessoa>;
  updatePessoa(id: number, pessoa: Partial<InsertPessoa>): Promise<Pessoa | undefined>;
  deletePessoa(id: number): Promise<boolean>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private testConfigurations: Map<number, TestConfiguration>;
  private testResults: Map<number, TestResult>;
  private pessoas: Map<number, Pessoa>;
  private currentUserId: number;
  private currentConfigId: number;
  private currentResultId: number;
  private currentPessoaId: number;

  constructor() {
    this.users = new Map();
    this.testConfigurations = new Map();
    this.testResults = new Map();
    this.pessoas = new Map();
    this.currentUserId = 1;
    this.currentConfigId = 1;
    this.currentResultId = 1;
    this.currentPessoaId = 1;
    
    // Initialize with preset configurations
    this.initializePresets();
  }

  private initializePresets() {
    const presets = [
      {
        name: "Performance",
        description: "Optimized for speed and low latency",
        config: {
          backend: "webgl",
          face: { enabled: true, detector: "mediapipe", mesh: false, iris: false, emotion: false, age: false, gender: false },
          body: { enabled: false },
          hand: { enabled: false },
          debug: false
        },
        isPreset: true
      },
      {
        name: "Accuracy",
        description: "Maximum accuracy with all features enabled",
        config: {
          backend: "webgl",
          face: { enabled: true, detector: "mediapipe", mesh: true, iris: true, emotion: true, age: true, gender: true, antispoof: true },
          body: { enabled: true },
          hand: { enabled: true },
          debug: false
        },
        isPreset: true
      },
      {
        name: "Mobile",
        description: "Optimized for mobile devices",
        config: {
          backend: "wasm",
          face: { enabled: true, detector: "mediapipe", mesh: false, iris: false, emotion: true, age: false, gender: false },
          body: { enabled: false },
          hand: { enabled: false },
          debug: false
        },
        isPreset: true
      },
      {
        name: "Desktop",
        description: "Full-featured desktop configuration",
        config: {
          backend: "webgl",
          face: { enabled: true, detector: "mediapipe", mesh: true, iris: true, emotion: true, age: true, gender: true, rotation: true },
          body: { enabled: true },
          hand: { enabled: true },
          gesture: { enabled: true },
          debug: false
        },
        isPreset: true
      }
    ];

    presets.forEach(preset => {
      const config: TestConfiguration = {
        id: this.currentConfigId++,
        name: preset.name,
        description: preset.description || null,
        config: preset.config,
        isPreset: preset.isPreset || false,
        createdAt: new Date()
      };
      this.testConfigurations.set(config.id, config);
    });
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async getTestConfiguration(id: number): Promise<TestConfiguration | undefined> {
    return this.testConfigurations.get(id);
  }

  async getTestConfigurations(): Promise<TestConfiguration[]> {
    return Array.from(this.testConfigurations.values());
  }

  async createTestConfiguration(config: InsertTestConfiguration): Promise<TestConfiguration> {
    const id = this.currentConfigId++;
    const testConfig: TestConfiguration = {
      id,
      name: config.name,
      description: config.description || null,
      config: config.config,
      isPreset: config.isPreset || false,
      createdAt: new Date()
    };
    this.testConfigurations.set(id, testConfig);
    return testConfig;
  }

  async updateTestConfiguration(id: number, config: Partial<InsertTestConfiguration>): Promise<TestConfiguration | undefined> {
    const existing = this.testConfigurations.get(id);
    if (!existing) return undefined;

    const updated = { ...existing, ...config };
    this.testConfigurations.set(id, updated);
    return updated;
  }

  async deleteTestConfiguration(id: number): Promise<boolean> {
    return this.testConfigurations.delete(id);
  }

  async getTestResults(configId?: number): Promise<TestResult[]> {
    const results = Array.from(this.testResults.values());
    if (configId) {
      return results.filter(result => result.configId === configId);
    }
    return results;
  }

  async createTestResult(result: InsertTestResult): Promise<TestResult> {
    const id = this.currentResultId++;
    const testResult: TestResult = {
      id,
      configId: result.configId || null,
      pessoaId: result.pessoaId || null,
      results: result.results,
      performance: result.performance,
      processingTime: result.processingTime,
      facesDetected: result.facesDetected,
      avgConfidence: result.avgConfidence,
      createdAt: new Date()
    };
    this.testResults.set(id, testResult);
    return testResult;
  }

  async getTestResultsByDateRange(startDate: Date, endDate: Date): Promise<TestResult[]> {
    return Array.from(this.testResults.values()).filter(
      result => result.createdAt && result.createdAt >= startDate && result.createdAt <= endDate
    );
  }

  async getPessoa(id: number): Promise<Pessoa | undefined> {
    return this.pessoas.get(id);
  }

  async getPessoas(): Promise<Pessoa[]> {
    return Array.from(this.pessoas.values());
  }

  async createPessoa(pessoa: InsertPessoa): Promise<Pessoa> {
    const id = this.currentPessoaId++;
    const novaPessoa: Pessoa = {
      id,
      nome: pessoa.nome,
      foto: pessoa.foto,
      descricao: pessoa.descricao || null,
      criadoEm: new Date(),
    };
    this.pessoas.set(id, novaPessoa);
    return novaPessoa;
  }

  async updatePessoa(id: number, pessoa: Partial<InsertPessoa>): Promise<Pessoa | undefined> {
    const existingPessoa = this.pessoas.get(id);
    if (!existingPessoa) return undefined;
    
    const updatedPessoa: Pessoa = { ...existingPessoa, ...pessoa };
    this.pessoas.set(id, updatedPessoa);
    return updatedPessoa;
  }

  async deletePessoa(id: number): Promise<boolean> {
    return this.pessoas.delete(id);
  }
}

// Database Storage Implementation
export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }

  async getTestConfiguration(id: number): Promise<TestConfiguration | undefined> {
    const [config] = await db.select().from(testConfigurations).where(eq(testConfigurations.id, id));
    return config || undefined;
  }

  async getTestConfigurations(): Promise<TestConfiguration[]> {
    return await db.select().from(testConfigurations);
  }

  async createTestConfiguration(config: InsertTestConfiguration): Promise<TestConfiguration> {
    const [testConfig] = await db.insert(testConfigurations).values(config).returning();
    return testConfig;
  }

  async updateTestConfiguration(id: number, config: Partial<InsertTestConfiguration>): Promise<TestConfiguration | undefined> {
    const [updated] = await db
      .update(testConfigurations)
      .set(config)
      .where(eq(testConfigurations.id, id))
      .returning();
    return updated || undefined;
  }

  async deleteTestConfiguration(id: number): Promise<boolean> {
    const result = await db.delete(testConfigurations).where(eq(testConfigurations.id, id));
    return (result.rowCount || 0) > 0;
  }

  async getTestResults(configId?: number): Promise<TestResult[]> {
    if (configId) {
      return await db.select().from(testResults).where(eq(testResults.configId, configId));
    }
    return await db.select().from(testResults);
  }

  async createTestResult(result: InsertTestResult): Promise<TestResult> {
    const [testResult] = await db.insert(testResults).values(result).returning();
    return testResult;
  }

  async getTestResultsByDateRange(startDate: Date, endDate: Date): Promise<TestResult[]> {
    return await db.select().from(testResults);
  }

  async getPessoa(id: number): Promise<Pessoa | undefined> {
    const [pessoa] = await db.select().from(pessoas).where(eq(pessoas.id, id));
    return pessoa || undefined;
  }

  async getPessoas(): Promise<Pessoa[]> {
    return await db.select().from(pessoas);
  }

  async createPessoa(pessoa: InsertPessoa): Promise<Pessoa> {
    const [novaPessoa] = await db.insert(pessoas).values(pessoa).returning();
    return novaPessoa;
  }

  async updatePessoa(id: number, pessoa: Partial<InsertPessoa>): Promise<Pessoa | undefined> {
    const [updated] = await db
      .update(pessoas)
      .set(pessoa)
      .where(eq(pessoas.id, id))
      .returning();
    return updated || undefined;
  }

  async deletePessoa(id: number): Promise<boolean> {
    const result = await db.delete(pessoas).where(eq(pessoas.id, id));
    return (result.rowCount || 0) > 0;
  }
}

// Initialize database with default configurations
async function initializeDatabase() {
  try {
    // Check if configurations already exist
    const existingConfigs = await db.select().from(testConfigurations);
    
    if (existingConfigs.length === 0) {
      // Insert default configurations
      const defaultConfigs = [
        {
          name: "Performance",
          description: "Configuração otimizada para performance",
          config: {
            backend: "cpu",
            modelBasePath: "https://cdn.jsdelivr.net/npm/@vladmandic/human-models/models/",
            face: { enabled: true, detector: { minConfidence: 0.7 } },
            body: { enabled: false },
            hand: { enabled: false },
            gesture: { enabled: false }
          },
          isPreset: true
        },
        {
          name: "Qualidade",
          description: "Configuração para melhor qualidade de detecção",
          config: {
            backend: "cpu",
            modelBasePath: "https://cdn.jsdelivr.net/npm/@vladmandic/human-models/models/",
            face: { 
              enabled: true, 
              detector: { minConfidence: 0.5 },
              mesh: { enabled: true },
              emotion: { enabled: true },
              age: { enabled: true },
              gender: { enabled: true }
            },
            body: { enabled: false },
            hand: { enabled: false },
            gesture: { enabled: false }
          },
          isPreset: true
        },
        {
          name: "Completa",
          description: "Configuração com todas as funcionalidades",
          config: {
            backend: "cpu",
            modelBasePath: "https://cdn.jsdelivr.net/npm/@vladmandic/human-models/models/",
            face: { 
              enabled: true, 
              detector: { minConfidence: 0.5 },
              mesh: { enabled: true },
              emotion: { enabled: true },
              age: { enabled: true },
              gender: { enabled: true }
            },
            body: { enabled: true },
            hand: { enabled: true },
            gesture: { enabled: true }
          },
          isPreset: true
        }
      ];
      
      await db.insert(testConfigurations).values(defaultConfigs);
      console.log("Configurações padrão inseridas no banco de dados");
    }
  } catch (error) {
    console.error("Erro ao inicializar banco de dados:", error);
  }
}

export const storage = new DatabaseStorage();

// Initialize database on startup
initializeDatabase();
