import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertTestConfigSchema, insertTestResultSchema, insertPessoaSchema } from "@shared/schema";
import { z } from "zod";

export async function registerRoutes(app: Express): Promise<Server> {
  // Test Configurations
  app.get("/api/test-configurations", async (req, res) => {
    try {
      const configs = await storage.getTestConfigurations();
      res.json(configs);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch test configurations" });
    }
  });

  app.get("/api/test-configurations/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const config = await storage.getTestConfiguration(id);
      if (!config) {
        return res.status(404).json({ message: "Configuration not found" });
      }
      res.json(config);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch configuration" });
    }
  });

  app.post("/api/test-configurations", async (req, res) => {
    try {
      const validatedData = insertTestConfigSchema.parse(req.body);
      const config = await storage.createTestConfiguration(validatedData);
      res.status(201).json(config);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid configuration data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create configuration" });
    }
  });

  app.put("/api/test-configurations/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const validatedData = insertTestConfigSchema.partial().parse(req.body);
      const config = await storage.updateTestConfiguration(id, validatedData);
      if (!config) {
        return res.status(404).json({ message: "Configuration not found" });
      }
      res.json(config);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid configuration data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to update configuration" });
    }
  });

  app.delete("/api/test-configurations/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const deleted = await storage.deleteTestConfiguration(id);
      if (!deleted) {
        return res.status(404).json({ message: "Configuration not found" });
      }
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: "Failed to delete configuration" });
    }
  });

  // Test Results
  app.get("/api/test-results", async (req, res) => {
    try {
      const configId = req.query.configId ? parseInt(req.query.configId as string) : undefined;
      const results = await storage.getTestResults(configId);
      res.json(results);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch test results" });
    }
  });

  app.post("/api/test-results", async (req, res) => {
    try {
      const validatedData = insertTestResultSchema.parse(req.body);
      const result = await storage.createTestResult(validatedData);
      res.status(201).json(result);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid result data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create test result" });
    }
  });

  // Human Library Processing Endpoint
  app.post("/api/process-image", async (req, res) => {
    try {
      const { imageData, config } = req.body;
      
      if (!imageData || !config) {
        return res.status(400).json({ message: "Image data and configuration are required" });
      }

      // In a real implementation, this would use the Human library
      // For now, we'll return mock data structure that matches what Human library would return
      const mockResults = {
        face: [
          {
            id: 1,
            score: 0.973,
            box: [100, 50, 200, 150],
            mesh: Array.from({ length: 468 }, (_, i) => [Math.random() * 200, Math.random() * 150]),
            age: 28,
            gender: "female",
            genderScore: 0.89,
            emotion: "happy",
            emotionScore: 0.94,
            iris: {
              left: { x: 130, y: 80 },
              right: { x: 170, y: 80 }
            }
          }
        ],
        performance: {
          total: 23,
          face: 12,
          emotion: 5,
          age: 3,
          gender: 3
        }
      };

      res.json(mockResults);
    } catch (error) {
      res.status(500).json({ message: "Failed to process image" });
    }
  });

  // Rotas para Pessoas
  app.get("/api/pessoas", async (req, res) => {
    try {
      const pessoas = await storage.getPessoas();
      res.json(pessoas);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar pessoas" });
    }
  });

  app.get("/api/pessoas/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const pessoa = await storage.getPessoa(id);
      if (!pessoa) {
        return res.status(404).json({ message: "Pessoa não encontrada" });
      }
      res.json(pessoa);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar pessoa" });
    }
  });

  app.post("/api/pessoas", async (req, res) => {
    try {
      const validatedData = insertPessoaSchema.parse(req.body);
      const pessoa = await storage.createPessoa(validatedData);
      res.status(201).json(pessoa);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Dados inválidos", errors: error.errors });
      }
      res.status(500).json({ message: "Erro ao cadastrar pessoa" });
    }
  });

  app.put("/api/pessoas/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const validatedData = insertPessoaSchema.partial().parse(req.body);
      const pessoa = await storage.updatePessoa(id, validatedData);
      if (!pessoa) {
        return res.status(404).json({ message: "Pessoa não encontrada" });
      }
      res.json(pessoa);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Dados inválidos", errors: error.errors });
      }
      res.status(500).json({ message: "Erro ao atualizar pessoa" });
    }
  });

  app.delete("/api/pessoas/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const deletado = await storage.deletePessoa(id);
      if (!deletado) {
        return res.status(404).json({ message: "Pessoa não encontrada" });
      }
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: "Erro ao deletar pessoa" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
