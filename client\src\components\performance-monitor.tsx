import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useQuery } from "@tanstack/react-query";
import { TestResult } from "@shared/schema";

interface PerformanceMonitorProps {
  config: any;
  performanceData: any;
  isProcessing: boolean;
}

export default function PerformanceMonitor({ config, performanceData, isProcessing }: PerformanceMonitorProps) {
  const [sessionTime, setSessionTime] = useState(0);
  const [testCount, setTestCount] = useState(0);
  const [systemMetrics, setSystemMetrics] = useState({
    fps: 0,
    gpu: 0,
    memory: 0,
    cpu: 0
  });

  const { data: testResults } = useQuery<TestResult[]>({
    queryKey: ['/api/test-results'],
  });

  useEffect(() => {
    const interval = setInterval(() => {
      setSessionTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (performanceData) {
      setTestCount(prev => prev + 1);
      // Update system metrics based on performance data
      setSystemMetrics(prev => ({
        ...prev,
        fps: Math.round(1000 / (performanceData.total || 100)),
        gpu: Math.min(100, Math.round(performanceData.total * 2)),
        memory: Math.round(2.3 + Math.random() * 0.5),
        cpu: Math.min(100, Math.round(performanceData.total * 1.5))
      }));
    }
  }, [performanceData]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleExportJSON = () => {
    const exportData = {
      config,
      results: testResults,
      performance: performanceData,
      timestamp: new Date().toISOString()
    };
    
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    const exportFileDefaultName = `human-test-results-${Date.now()}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const handleCopyCode = () => {
    const codeTemplate = `
import { Human } from '@vladmandic/human';

const config = ${JSON.stringify(config, null, 2)};
const human = new Human(config);

async function detectFaces(input) {
  const result = await human.detect(input);
  return result;
}
    `;
    
    navigator.clipboard.writeText(codeTemplate);
  };

  return (
    <div className="p-4">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Performance Monitor</h2>
      
      {/* Performance Metrics */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-3">System Performance</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">FPS</span>
            <span className="text-sm font-medium text-green-600">{systemMetrics.fps}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">GPU Usage</span>
            <span className="text-sm font-medium text-orange-600">{systemMetrics.gpu}%</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Memory</span>
            <span className="text-sm font-medium text-blue-600">{systemMetrics.memory}GB</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">CPU Usage</span>
            <span className="text-sm font-medium text-gray-700">{systemMetrics.cpu}%</span>
          </div>
        </div>
      </div>

      {/* Processing Timeline */}
      {performanceData && (
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Processing Timeline</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">Face Detection</span>
              <span className="font-medium">{performanceData.face || 0}ms</span>
            </div>
            <Progress value={(performanceData.face || 0) / (performanceData.total || 1) * 100} className="h-2" />
            
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">Feature Analysis</span>
              <span className="font-medium">{performanceData.emotion || 0}ms</span>
            </div>
            <Progress value={(performanceData.emotion || 0) / (performanceData.total || 1) * 100} className="h-2" />
            
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">Age/Gender</span>
              <span className="font-medium">{performanceData.age || 0}ms</span>
            </div>
            <Progress value={(performanceData.age || 0) / (performanceData.total || 1) * 100} className="h-2" />
          </div>
        </div>
      )}

      {/* Test Results History */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Test Results History</h3>
        <div className="space-y-2 max-h-40 overflow-y-auto">
          {testResults?.slice(-5).map((result, index) => (
            <div key={result.id} className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium text-gray-900">Test #{result.id}</span>
                <span className="text-xs text-gray-500">Recent</span>
              </div>
              <div className="text-xs text-gray-600">
                {result.facesDetected} faces detected, {result.avgConfidence}% avg confidence
              </div>
              <div className="flex items-center justify-between mt-1">
                <span className="text-xs text-gray-500">Processing: {result.processingTime}ms</span>
                <button className="text-xs text-blue-600 hover:text-blue-700">View</button>
              </div>
            </div>
          ))}
          
          {!testResults?.length && (
            <div className="text-xs text-gray-500 text-center py-4">
              No test results yet
            </div>
          )}
        </div>
      </div>

      {/* Configuration Export */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Configuration Export</h3>
        <div className="space-y-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportJSON}
            className="w-full text-sm"
          >
            📥 Download JSON
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopyCode}
            className="w-full text-sm"
          >
            📋 Copy Code
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="w-full text-sm"
            onClick={() => navigator.share && navigator.share({
              title: 'Human Library Configuration',
              text: JSON.stringify(config, null, 2)
            })}
          >
            📤 Share Config
          </Button>
        </div>
      </div>

      {/* Error Log */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-900">Error Log</h3>
          <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">No Errors</span>
        </div>
        <div className="text-xs text-gray-500 text-center py-4">
          No errors detected in current session
        </div>
      </div>
    </div>
  );
}
