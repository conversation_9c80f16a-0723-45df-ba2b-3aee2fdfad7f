import { pgTable, text, serial, integer, boolean, jsonb, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const testConfigurations = pgTable("test_configurations", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  config: jsonb("config").notNull(),
  isPreset: boolean("is_preset").default(false),
  createdAt: timestamp("created_at").defaultNow(),
});

export const pessoas = pgTable("pessoas", {
  id: serial("id").primaryKey(),
  nome: text("nome").notNull(),
  foto: text("foto").notNull(), // Base64 da imagem ou URL
  descricao: text("descricao"),
  criadoEm: timestamp("criado_em").defaultNow(),
});

export const testResults = pgTable("test_results", {
  id: serial("id").primaryKey(),
  configId: integer("config_id").references(() => testConfigurations.id),
  pessoaId: integer("pessoa_id").references(() => pessoas.id),
  results: jsonb("results").notNull(),
  performance: jsonb("performance").notNull(),
  processingTime: integer("processing_time").notNull(),
  facesDetected: integer("faces_detected").notNull(),
  avgConfidence: integer("avg_confidence").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export const insertTestConfigSchema = createInsertSchema(testConfigurations).pick({
  name: true,
  description: true,
  config: true,
  isPreset: true,
});

export const insertPessoaSchema = createInsertSchema(pessoas).pick({
  nome: true,
  foto: true,
  descricao: true,
});

export const insertTestResultSchema = createInsertSchema(testResults).pick({
  configId: true,
  pessoaId: true,
  results: true,
  performance: true,
  processingTime: true,
  facesDetected: true,
  avgConfidence: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type TestConfiguration = typeof testConfigurations.$inferSelect;
export type InsertTestConfiguration = z.infer<typeof insertTestConfigSchema>;
export type TestResult = typeof testResults.$inferSelect;
export type InsertTestResult = z.infer<typeof insertTestResultSchema>;
export type Pessoa = typeof pessoas.$inferSelect;
export type InsertPessoa = z.infer<typeof insertPessoaSchema>;
