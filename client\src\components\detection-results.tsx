import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface DetectionResultsProps {
  results: any;
  isProcessing: boolean;
}

export default function DetectionResults({ results, isProcessing }: DetectionResultsProps) {
  const facesDetected = results?.face?.length || 0;
  const avgConfidence = results?.face ? Math.round(results.face.reduce((acc: number, face: any) => acc + face.score, 0) / results.face.length * 100) : 0;
  const processingTime = results?.performance?.total || 0;

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium text-gray-900">Detection Results</CardTitle>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-green-600">Real-time</span>
            <div className={`w-2 h-2 rounded-full ${isProcessing ? 'bg-green-500 animate-pulse' : 'bg-gray-300'}`}></div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        {/* Detection Overview */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{facesDetected}</div>
            <div className="text-xs text-gray-500">Faces Detected</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{avgConfidence}%</div>
            <div className="text-xs text-gray-500">Avg Confidence</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{processingTime}ms</div>
            <div className="text-xs text-gray-500">Processing Time</div>
          </div>
        </div>
        
        {/* Detailed Results */}
        <div className="space-y-3">
          {results?.face?.map((face: any, index: number) => (
            <div key={index} className="border border-gray-200 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">Face #{index + 1}</h4>
                <span className={`text-xs font-medium px-2 py-1 rounded ${
                  face.score > 0.9 ? 'text-green-600 bg-green-100' : 
                  face.score > 0.8 ? 'text-orange-600 bg-orange-100' : 
                  'text-red-600 bg-red-100'
                }`}>
                  {Math.round(face.score * 100)}%
                </span>
              </div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-gray-500">Age:</span>
                  <span className="font-medium ml-1">{face.age || 'N/A'}</span>
                </div>
                <div>
                  <span className="text-gray-500">Gender:</span>
                  <span className="font-medium ml-1">
                    {face.gender ? `${face.gender} (${Math.round(face.genderScore * 100)}%)` : 'N/A'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Emotion:</span>
                  <span className="font-medium ml-1">
                    {face.emotion ? `${face.emotion} (${Math.round(face.emotionScore * 100)}%)` : 'N/A'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Landmarks:</span>
                  <span className="font-medium ml-1">{face.mesh?.length || 0} points</span>
                </div>
              </div>
              <div className="mt-2">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500">Confidence:</span>
                  <span className="font-medium">{Math.round(face.score * 100)}%</span>
                </div>
                <Progress value={face.score * 100} className="mt-1" />
              </div>
            </div>
          ))}
          
          {!results && (
            <div className="text-center text-gray-500 py-8">
              No detection results yet. Start processing to see results here.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
