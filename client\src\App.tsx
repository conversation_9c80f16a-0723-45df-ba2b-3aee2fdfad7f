import { Switch, Route, Link, useLocation } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import NotFound from "@/pages/not-found";
import TesteHuman from "@/pages/human-testing";
import CadastroPessoas from "@/pages/cadastro-pessoas";
import { Camera, Users, TestTube } from "lucide-react";

function Navigation() {
  const [location] = useLocation();
  
  return (
    <nav className="border-b bg-background">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <TestTube className="h-6 w-6" />
            <h1 className="text-xl font-bold">Teste Human Library</h1>
          </div>
          <div className="flex space-x-2">
            <Button
              variant={location === "/" ? "default" : "outline"}
              asChild
              size="sm"
            >
              <Link href="/">
                <Camera className="mr-2 h-4 w-4" />
                Reconhecimento Facial
              </Link>
            </Button>
            <Button
              variant={location === "/pessoas" ? "default" : "outline"}
              asChild
              size="sm"
            >
              <Link href="/pessoas">
                <Users className="mr-2 h-4 w-4" />
                Cadastro de Pessoas
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
}

function Router() {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <Switch>
        <Route path="/" component={TesteHuman} />
        <Route path="/pessoas" component={CadastroPessoas} />
        <Route component={NotFound} />
      </Switch>
    </div>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
